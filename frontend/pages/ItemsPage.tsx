import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Search, 
  Filter, 
  SortAsc, 
  Gem,
  Shirt,
  Image,
  Zap,
  User,
  Package,
  Grid3X3,
  Star,
  TrendingUp,
  Eye,
  X
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { 
  swimsuitsData, 
  accessoriesData, 
  skillsData, 
  bromidesData} from '@/data';

type ItemType = 'all' | 'swimsuit' | 'accessory' | 'skill' | 'bromide';
type SortOption = 'name' | 'type' | 'stats' | 'rarity';
type SortDirection = 'asc' | 'desc';

type Language = 'EN' | 'CN' | 'TW' | 'KO' | 'JP';

interface UnifiedItem {
  id: string;
  name: string;
  type: ItemType;
  category?: string;
  rarity?: string;
  stats?: { [key: string]: number };
  skill?: any;
  character?: string;
  description?: string;
  image?: string;
  translations?: {
    [key in Language]?: {
      name: string;
      description?: string;
    };
  };
}

const ItemsPage: React.FC = () => {
  // State management
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedLanguage, setSelectedLanguage] = useState<Language>('EN');
  const [selectedType, setSelectedType] = useState<ItemType>('all');
  const [sortBy, setSortBy] = useState<SortOption>('name');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');

  const [showFilters, setShowFilters] = useState(false);
  const [selectedRarity, setSelectedRarity] = useState<string>('');
  const [selectedCharacter, setSelectedCharacter] = useState<string>('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Mock translations for demonstration
  const getItemTranslations = (item: any, originalType: string): any => {
    return {
      EN: { name: item.name, description: item.description || '' },
      CN: { name: `中文_${item.name}`, description: `中文_${item.description || ''}` },
      TW: { name: `繁體_${item.name}`, description: `繁體_${item.description || ''}` },
      KO: { name: `한국_${item.name}`, description: `한국_${item.description || ''}` },
      JP: { name: `日本_${item.name}`, description: `日本_${item.description || ''}` }
    };
  };

  // Unified items data
  const unifiedItems: UnifiedItem[] = useMemo(() => {
    const items: UnifiedItem[] = [];

    // Add swimsuits
    swimsuitsData.forEach(swimsuit => {
      items.push({
        id: `swimsuit-${swimsuit.id}`,
        name: swimsuit.name,
        type: 'swimsuit' as ItemType,
        rarity: swimsuit.rarity,
        stats: swimsuit.stats,
        character: swimsuit.character,
        image: `/images/swimsuits/${swimsuit.id}.jpg`,
        translations: getItemTranslations(swimsuit, 'swimsuit')
      });
    });

    // Add accessories
    accessoriesData.forEach(accessory => {
      items.push({
        id: `accessory-${accessory.id}`,
        name: accessory.name,
        type: 'accessory' as ItemType,
        category: accessory.type,
        stats: accessory.stats,
        skill: accessory.skill,
        image: `/images/accessories/${accessory.id}.jpg`,
        translations: getItemTranslations(accessory, 'accessory')
      });
    });

    // Add skills
    skillsData.forEach(skill => {
      items.push({
        id: `skill-${skill.id}`,
        name: skill.name,
        type: 'skill' as ItemType,
        category: skill.type,
        description: skill.description,
        image: `/images/skills/${skill.id}.jpg`,
        translations: getItemTranslations(skill, 'skill')
      });
    });

    // Add bromides
    bromidesData.forEach(bromide => {
      items.push({
        id: `bromide-${bromide.id}`,
        name: bromide.name,
        type: 'bromide' as ItemType,
        rarity: bromide.rarity,
        image: `/images/bromides/${bromide.id}.jpg`,
        translations: getItemTranslations(bromide, 'bromide')
      });
    });

    return items;
  }, []);

  // Filtering and sorting
  const filteredAndSortedItems = useMemo(() => {
    let filtered = unifiedItems.filter(item => {
      // Type filter
      const typeMatch = selectedType === 'all' || item.type === selectedType;
      
      // Text search (multi-language)
      const searchText = searchTerm.toLowerCase();
      const translation = item.translations?.[selectedLanguage];
      const nameMatch = 
        item.name.toLowerCase().includes(searchText) ||
        (translation?.name.toLowerCase().includes(searchText)) ||
        (item.description?.toLowerCase().includes(searchText)) ||
        (translation?.description?.toLowerCase().includes(searchText));
      
      // Rarity filter
      const rarityMatch = !selectedRarity || item.rarity === selectedRarity;
      
      // Character filter
      const characterMatch = !selectedCharacter || item.character === selectedCharacter;
      
      return typeMatch && nameMatch && rarityMatch && characterMatch;
    });

    // Sorting
    filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'name':
          const aName = a.translations?.[selectedLanguage]?.name || a.name;
          const bName = b.translations?.[selectedLanguage]?.name || b.name;
          comparison = aName.localeCompare(bName);
          break;
        case 'type':
          comparison = a.type.localeCompare(b.type);
          break;
        case 'stats':
          const aTotal = a.stats ? Object.values(a.stats).reduce((sum, val) => sum + val, 0) : 0;
          const bTotal = b.stats ? Object.values(b.stats).reduce((sum, val) => sum + val, 0) : 0;
          comparison = aTotal - bTotal;
          break;
        case 'rarity':
          const rarityOrder = { 'SSR': 3, 'SR': 2, 'R': 1, '': 0 };
          comparison = (rarityOrder[a.rarity as keyof typeof rarityOrder] || 0) - 
                      (rarityOrder[b.rarity as keyof typeof rarityOrder] || 0);
          break;
      }
      
      return sortDirection === 'desc' ? -comparison : comparison;
    });

    return filtered;
  }, [unifiedItems, searchTerm, selectedLanguage, selectedType, selectedRarity, selectedCharacter, sortBy, sortDirection]);

  // Get unique values for filters
  const uniqueRarities = useMemo(() => 
    [...new Set(unifiedItems.map(item => item.rarity).filter(Boolean))], [unifiedItems]);
  const uniqueCharacters = useMemo(() => 
    [...new Set(unifiedItems.map(item => item.character).filter(Boolean))], [unifiedItems]);

  // Helper functions
  const getTypeIcon = (type: ItemType) => {
    switch (type) {
      case 'swimsuit': return <Shirt className="w-4 h-4" />;
      case 'accessory': return <Gem className="w-4 h-4" />;
      case 'skill': return <Zap className="w-4 h-4" />;
      case 'bromide': return <Image className="w-4 h-4" />;
      default: return <Package className="w-4 h-4" />;
    }
  };

  const getTypeColor = (type: ItemType) => {
    switch (type) {
      case 'swimsuit': return 'text-blue-400 bg-blue-400/10 border-blue-400/20';
      case 'accessory': return 'text-purple-400 bg-purple-400/10 border-purple-400/20';
      case 'skill': return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/20';
      case 'bromide': return 'text-pink-400 bg-pink-400/10 border-pink-400/20';
      default: return 'text-gray-400 bg-gray-400/10 border-gray-400/20';
    }
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'SSR': return 'text-yellow-400 bg-gradient-to-r from-yellow-400/20 to-orange-400/20 border-yellow-400/30';
      case 'SR': return 'text-purple-400 bg-gradient-to-r from-purple-400/20 to-pink-400/20 border-purple-400/30';
      case 'R': return 'text-blue-400 bg-gradient-to-r from-blue-400/20 to-cyan-400/20 border-blue-400/30';
      default: return 'text-gray-400 bg-gray-400/10 border-gray-400/20';
    }
  };

  const clearFilters = () => {
    setSearchTerm('');
    setSelectedType('all');
    setSelectedRarity('');
    setSelectedCharacter('');
    setSortBy('name');
    setSortDirection('asc');
  };

  const handleSortChange = (newSortBy: SortOption) => {
    if (sortBy === newSortBy) {
      setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(newSortBy);
      setSortDirection('asc');
    }
  };

  // Active filters count
  const activeFiltersCount = [
    selectedType !== 'all',
    selectedRarity !== '',
    selectedCharacter !== '',
    searchTerm !== ''
  ].filter(Boolean).length;

  // Sort button component
  const SortButton = ({ sortKey, children }: { sortKey: SortOption; children: React.ReactNode }) => (
    <motion.button
      onClick={() => handleSortChange(sortKey)}
      whileHover={{ scale: 1.05, y: -2 }}
      whileTap={{ scale: 0.95 }}
      className={cn(
        'px-4 py-2.5 rounded-xl transition-all flex items-center gap-2 text-sm font-medium border shadow-sm',
        sortBy === sortKey
          ? 'bg-gradient-to-r from-accent-cyan to-accent-purple text-white shadow-lg border-accent-cyan/30'
          : 'bg-card/80 backdrop-blur-sm text-muted-foreground hover:text-foreground hover:bg-accent-cyan/20 border-border/50 hover:border-accent-cyan/50'
      )}
    >
      {children}
      {sortBy === sortKey && (
        <motion.div
          initial={{ rotate: 0 }}
          animate={{ rotate: sortDirection === 'desc' ? 180 : 0 }}
          transition={{ duration: 0.2 }}
        >
          <SortAsc className="w-3 h-3" />
        </motion.div>
      )}
    </motion.button>
  );

  // Item card component
  const ItemCard = ({ item }: { item: UnifiedItem }) => {
    const translation = item.translations?.[selectedLanguage];
    const displayName = translation?.name || item.name;
    const displayDescription = translation?.description || item.description;

    return (
      <motion.div
        layout
        initial={{ opacity: 0, y: 20, scale: 0.9 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        exit={{ opacity: 0, y: -20, scale: 0.9 }}
        whileHover={{ y: -8, scale: 1.02 }}
        transition={{ duration: 0.3, type: "spring", stiffness: 300 }}
        className="doax-card overflow-hidden cursor-pointer group relative border border-border/50 hover:border-accent-cyan/50 shadow-lg hover:shadow-2xl transition-all duration-500"
      >
        {/* Gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-accent-cyan/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        
        <div className="relative p-5">
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center gap-3 flex-1 min-w-0">
              <div className="relative w-14 h-14 rounded-xl overflow-hidden bg-gradient-to-br from-muted/50 to-muted/70 flex-shrink-0 border border-border/30 shadow-inner">
                {item.image ? (
                  <img
                    src={item.image}
                    alt={displayName}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      const fallback = target.nextElementSibling as HTMLElement;
                      if (fallback) fallback.style.display = 'flex';
                    }}
                  />
                ) : null}
                <div 
                  className={cn(
                    'absolute inset-0 flex items-center justify-center text-white bg-gradient-to-br from-muted/70 to-muted/90',
                    item.image ? 'hidden' : 'flex'
                  )}
                  style={{ display: item.image ? 'none' : 'flex' }}
                >
                  {getTypeIcon(item.type)}
                </div>
                
                {/* Rarity glow effect */}
                {item.rarity && (
                  <div className={cn(
                    'absolute inset-0 rounded-xl',
                    item.rarity === 'SSR' && 'shadow-[0_0_20px_rgba(255,215,0,0.3)]',
                    item.rarity === 'SR' && 'shadow-[0_0_15px_rgba(147,51,234,0.3)]',
                    item.rarity === 'R' && 'shadow-[0_0_10px_rgba(59,130,246,0.3)]'
                  )} />
                )}
              </div>
              
              <div className="flex-1 min-w-0">
                <h3 className="font-bold text-foreground group-hover:text-accent-cyan transition-colors duration-300 text-base truncate">
                  {displayName}
                </h3>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant="outline" className={cn('text-xs border', getTypeColor(item.type))}>
                    {getTypeIcon(item.type)}
                    <span className="ml-1 capitalize">{item.type}</span>
                  </Badge>
                </div>
              </div>
            </div>
            
            {item.rarity && (
              <motion.div
                whileHover={{ scale: 1.1, rotate: 5 }}
                className="flex-shrink-0"
              >
                <Badge className={cn('text-xs font-bold border shadow-sm', getRarityColor(item.rarity))}>
                  <Star className="w-3 h-3 mr-1" />
                  {item.rarity}
                </Badge>
              </motion.div>
            )}
          </div>
          
          {displayDescription && (
            <motion.p 
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.1 }}
              className="text-sm text-muted-foreground mb-4 line-clamp-2 leading-relaxed"
            >
              {displayDescription}
            </motion.p>
          )}
          
          {item.stats && (
            <motion.div 
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="grid grid-cols-2 gap-2 mb-4 p-3 bg-muted/30 rounded-lg border border-border/30"
            >
              {Object.entries(item.stats).map(([stat, value], index) => (
                <motion.div 
                  key={stat}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.3 + index * 0.1 }}
                  className="flex justify-between text-xs"
                >
                  <span className="text-muted-foreground uppercase font-medium">{stat}:</span>
                  <span className="font-bold text-accent-cyan">{value}</span>
                </motion.div>
              ))}
            </motion.div>
          )}
          
          {item.character && (
            <motion.div 
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.4 }}
              className="flex items-center gap-2 text-xs text-muted-foreground bg-background/50 px-2 py-1.5 rounded-lg border border-border/30"
            >
              <User className="w-3 h-3 text-accent-pink" />
              <span className="font-medium">{item.character}</span>
            </motion.div>
          )}
        </div>

        {/* Hover effects */}
        <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <motion.div
            initial={{ scale: 0 }}
            whileHover={{ scale: 1.2 }}
            className="w-8 h-8 bg-accent-cyan/20 backdrop-blur-sm rounded-full flex items-center justify-center border border-accent-cyan/30"
          >
            <Eye className="w-4 h-4 text-accent-cyan" />
          </motion.div>
        </div>
      </motion.div>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20 space-y-8">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="doax-card p-8 relative overflow-hidden border border-border/50 shadow-xl"
      >
        {/* Background pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-accent-cyan to-accent-purple rounded-full blur-3xl" />
          <div className="absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-tl from-accent-pink to-accent-purple rounded-full blur-3xl" />
        </div>
        
        <div className="relative flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
          <div className="space-y-3">
            <motion.h1 
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
              className="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-accent-pink via-accent-purple to-accent-cyan bg-clip-text text-transparent"
            >
              Items Collection
            </motion.h1>
            <motion.p 
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 }}
              className="text-muted-foreground text-lg"
            >
              Discover all items with multi-language search support
            </motion.p>
            <motion.div 
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4 }}
              className="flex items-center gap-4 text-sm text-muted-foreground"
            >
              <div className="flex items-center gap-1">
                <Package className="w-4 h-4 text-accent-cyan" />
                <span>{unifiedItems.length} Total Items</span>
              </div>
              <div className="flex items-center gap-1">
                <TrendingUp className="w-4 h-4 text-accent-pink" />
                <span>{filteredAndSortedItems.length} Showing</span>
              </div>
            </motion.div>
          </div>
          
          {/* Language Selector */}
          <motion.div 
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.5 }}
            className="flex items-center gap-3 bg-card/80 backdrop-blur-sm p-4 rounded-xl border border-border/50 shadow-lg"
          >
            <span className="text-sm font-medium text-muted-foreground whitespace-nowrap">Language:</span>
            <select
              value={selectedLanguage}
              onChange={(e) => setSelectedLanguage(e.target.value as Language)}
              className="bg-background/80 border border-border/50 rounded-lg px-3 py-2 text-sm focus:outline-none focus:border-accent-cyan focus:ring-2 focus:ring-accent-cyan/20 transition-all font-medium"
            >
              <option value="EN">🇺🇸 English</option>
              <option value="CN">🇨🇳 中文</option>
              <option value="TW">🇹🇼 繁體中文</option>
              <option value="KO">🇰🇷 한국어</option>
              <option value="JP">🇯🇵 日本語</option>
            </select>
          </motion.div>
        </div>
      </motion.div>

      {/* Search and Filter Controls */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="space-y-4"
      >
        <div className="flex flex-col xl:flex-row gap-4 items-stretch xl:items-center">
          {/* Search Bar */}
          <div className="flex-1 relative group">
            <Search className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-muted-foreground group-focus-within:text-accent-cyan transition-colors" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full bg-card/80 backdrop-blur-sm border border-border/50 rounded-xl pl-12 pr-12 py-4 focus:outline-none focus:border-accent-cyan focus:ring-2 focus:ring-accent-cyan/20 transition-all placeholder-muted-foreground text-base shadow-lg"
              placeholder={`🔍 Search items in ${selectedLanguage}...`}
            />
            <AnimatePresence>
              {searchTerm && (
                <motion.button
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  onClick={() => setSearchTerm('')}
                  className="absolute right-4 top-1/2 -translate-y-1/2 w-6 h-6 text-muted-foreground hover:text-accent-cyan transition-colors bg-muted/50 rounded-full flex items-center justify-center"
                >
                  <X className="w-4 h-4" />
                </motion.button>
              )}
            </AnimatePresence>
          </div>

          {/* Control Buttons */}
          <div className="flex items-center gap-3">
            <motion.button
              onClick={() => setShowFilters(!showFilters)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className={cn(
                'px-6 py-4 rounded-xl transition-all flex items-center gap-3 border shadow-lg relative',
                showFilters 
                  ? 'bg-gradient-to-r from-accent-cyan to-accent-purple text-white shadow-xl border-accent-cyan/30' 
                  : 'bg-card/80 backdrop-blur-sm border-border/50 text-muted-foreground hover:text-foreground hover:bg-accent-cyan/20 hover:border-accent-cyan/50'
              )}
            >
              <Filter className="w-5 h-5" />
              <span className="text-sm font-medium">Advanced Filters</span>
              {activeFiltersCount > 0 && (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="absolute -top-2 -right-2 w-6 h-6 bg-accent-pink text-white text-xs font-bold rounded-full flex items-center justify-center shadow-lg"
                >
                  {activeFiltersCount}
                </motion.div>
              )}
            </motion.button>


            <div className="text-sm text-muted-foreground bg-card/80 backdrop-blur-sm px-6 py-4 rounded-xl border border-border/50 shadow-lg">
              <span className="text-accent-cyan font-bold text-lg">{filteredAndSortedItems.length}</span>
              <span className="ml-1">found</span>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Advanced Filters */}
      <AnimatePresence>
        {showFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0, y: -20 }}
            animate={{ opacity: 1, height: 'auto', y: 0 }}
            exit={{ opacity: 0, height: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <div className="doax-card p-8 border border-border/50 shadow-xl relative overflow-hidden">
              {/* Background gradient */}
              <div className="absolute inset-0 bg-gradient-to-r from-accent-cyan/5 to-accent-purple/5" />
              
              <div className="relative">
                <div className="flex items-center justify-between mb-8">
                  <motion.h3 
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="text-2xl font-bold text-foreground flex items-center"
                  >
                    <Filter className="w-6 h-6 mr-3 text-accent-cyan" />
                    Advanced Filters
                  </motion.h3>
                  
                  <motion.button
                    onClick={() => setShowFilters(false)}
                    whileHover={{ scale: 1.1, rotate: 90 }}
                    whileTap={{ scale: 0.9 }}
                    className="w-8 h-8 bg-muted/50 hover:bg-accent-pink/20 rounded-full flex items-center justify-center transition-colors"
                  >
                    <X className="w-4 h-4" />
                  </motion.button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 }}
                  >
                    <label className="block text-sm font-semibold text-foreground mb-3">Item Type</label>
                    <select
                      value={selectedType}
                      onChange={(e) => setSelectedType(e.target.value as ItemType)}
                      className="w-full bg-background/80 border border-border/50 rounded-xl px-4 py-3 text-sm focus:outline-none focus:border-accent-cyan focus:ring-2 focus:ring-accent-cyan/20 transition-all shadow-sm"
                    >
                      <option value="all">🌟 All Types</option>
                      <option value="swimsuit">👙 Swimsuits</option>
                      <option value="accessory">💎 Accessories</option>
                      <option value="skill">⚡ Skills</option>
                      <option value="bromide">🖼️ Bromides</option>
                    </select>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                  >
                    <label className="block text-sm font-semibold text-foreground mb-3">Rarity</label>
                    <select
                      value={selectedRarity}
                      onChange={(e) => setSelectedRarity(e.target.value)}
                      className="w-full bg-background/80 border border-border/50 rounded-xl px-4 py-3 text-sm focus:outline-none focus:border-accent-cyan focus:ring-2 focus:ring-accent-cyan/20 transition-all shadow-sm"
                    >
                      <option value="">✨ All Rarities</option>
                      {uniqueRarities.map(rarity => (
                        <option key={rarity} value={rarity}>
                          {rarity === 'SSR' && '🌟'} 
                          {rarity === 'SR' && '💜'} 
                          {rarity === 'R' && '💙'} 
                          {rarity}
                        </option>
                      ))}
                    </select>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                  >
                    <label className="block text-sm font-semibold text-foreground mb-3">Character</label>
                    <select
                      value={selectedCharacter}
                      onChange={(e) => setSelectedCharacter(e.target.value)}
                      className="w-full bg-background/80 border border-border/50 rounded-xl px-4 py-3 text-sm focus:outline-none focus:border-accent-cyan focus:ring-2 focus:ring-accent-cyan/20 transition-all shadow-sm"
                    >
                      <option value="">👥 All Characters</option>
                      {uniqueCharacters.map(character => (
                        <option key={character} value={character}>{character}</option>
                      ))}
                    </select>
                  </motion.div>
                </div>

                <motion.div 
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                  className="flex flex-wrap gap-3 mb-8 p-4 bg-muted/30 rounded-xl border border-border/30"
                >
                  <span className="text-sm font-semibold text-foreground flex items-center mr-3">
                    <SortAsc className="w-4 h-4 mr-2 text-accent-cyan" />
                    Sort by:
                  </span>
                  <SortButton sortKey="name">📝 Name</SortButton>
                  <SortButton sortKey="type">🏷️ Type</SortButton>
                  <SortButton sortKey="stats">📊 Stats</SortButton>
                  <SortButton sortKey="rarity">⭐ Rarity</SortButton>
                </motion.div>

                <motion.div 
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                  className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4"
                >
                  <motion.button
                    onClick={clearFilters}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="bg-gradient-to-r from-accent-pink/20 to-accent-purple/20 hover:from-accent-pink/30 hover:to-accent-purple/30 text-accent-pink border border-accent-pink/30 rounded-xl px-6 py-3 text-sm font-semibold transition-all shadow-lg"
                  >
                    🔄 Clear All Filters
                  </motion.button>
                  <div className="text-sm text-muted-foreground bg-background/50 px-4 py-2 rounded-lg border border-border/30">
                    Showing <span className="text-accent-cyan font-bold">{filteredAndSortedItems.length}</span> of <span className="font-semibold">{unifiedItems.length}</span> items
                  </div>
                </motion.div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Items Grid */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.2 }}
        className={cn(
          'grid gap-6',
          viewMode === 'grid' 
            ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5' 
            : 'grid-cols-1 max-w-4xl mx-auto'
        )}
      >
        <AnimatePresence mode="popLayout">
          {filteredAndSortedItems.length === 0 ? (
            <motion.div
              key="no-results"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              className="col-span-full doax-card p-16 text-center border border-border/50 shadow-xl relative overflow-hidden"
            >
              {/* Background decoration */}
              <div className="absolute inset-0 bg-gradient-to-br from-muted/20 to-transparent" />
              
              <div className="relative">
                <motion.div
                  animate={{ 
                    scale: [1, 1.1, 1],
                    rotate: [0, 5, -5, 0]
                  }}
                  transition={{ 
                    duration: 2,
                    repeat: Infinity,
                    repeatType: "reverse"
                  }}
                  className="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-accent-cyan/20 to-accent-purple/20 rounded-full flex items-center justify-center border-2 border-accent-cyan/30"
                >
                  <Package className="w-12 h-12 text-accent-cyan" />
                </motion.div>
                <h3 className="text-2xl font-bold mb-3 text-foreground">No items found</h3>
                <p className="text-muted-foreground text-lg mb-6">Try adjusting your search filters or search terms</p>
                <motion.button
                  onClick={clearFilters}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="bg-gradient-to-r from-accent-cyan to-accent-purple text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all"
                >
                  🔄 Reset Filters
                </motion.button>
              </div>
            </motion.div>
          ) : (
            filteredAndSortedItems.map((item, index) => (
              <ItemCard key={item.id} item={item} />
            ))
          )}
        </AnimatePresence>
      </motion.div>
    </div>
  );
};

export default ItemsPage; 