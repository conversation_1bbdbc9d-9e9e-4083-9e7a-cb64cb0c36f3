import React, { useState, useEffect } from 'react';
import {
  Settings,
  FileText,
  Calendar,
  Edit3,
  Save,
  Plus,
  Trash2,
  BookOpen,
  Tags,
  Download,
  Upload,
  FileDown,
  FileUp,
  X,
  Eye,
  Maximize2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { documentsData, documentCategoriesData, type Document, type DocumentCategory } from '@/data';
import { useUpdateLogs } from '@/contexts/UpdateLogsContext';
import { UpdateLog } from '@/data/updateLogs';
import TiptapEditor from '@/components/TiptapEditor';
import { Container, Section, Card, Grid, Stack, Inline, FormGroup, StatusBadge } from '@/components/ui/spacing';
import { SaveButton, LoadingState } from '@/components/ui/loading';

interface AdminSection {
  id: string;
  title: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
  lastUpdated: string;
  status: 'active' | 'inactive' | 'draft';
}

// UpdateLog interface is now imported from @/data/updateLogs

interface SiteContent {
  id: string;
  page: string;
  section: string;
  content: string;
  lastModified: string;
  modifiedBy: string;
}

const AdminPage: React.FC = () => {
  const [documents, setDocuments] = useState<Document[]>(documentsData);
  const [documentCategories, setDocumentCategories] = useState<DocumentCategory[]>(documentCategoriesData);
  const { updateLogs, addUpdateLog, updateUpdateLog, deleteUpdateLog, isLoading } = useUpdateLogs();
  
  const addDocument = (document: Document) => {
    setDocuments(prev => [document, ...prev]);
  };

  const updateDocument = (id: string, updates: Partial<Document>) => {
    setDocuments(prev => prev.map(doc => 
      doc.id === id ? { ...doc, ...updates } : doc
    ));
  };

  const deleteDocument = (id: string) => {
    setDocuments(prev => prev.filter(doc => doc.id !== id));
  };
  
  const [activeTab, setActiveTab] = useState<string>('documents');
  const [siteContent, setSiteContent] = useState<SiteContent[]>([]);
  const [editingLog, setEditingLog] = useState<UpdateLog | null>(null);
  const [editingDocument, setEditingDocument] = useState<Document | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [csvData, setCsvData] = useState<string>('');
  const [csvImportType, setCsvImportType] = useState<'documents' | 'update-logs'>('documents');
  const [importPage, setImportPage] = useState<string>('all');
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [isProcessingFile, setIsProcessingFile] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Available pages for import
  const availablePages = [
    { id: 'accessory', name: 'Accessory' },
    { id: 'decoratebromide', name: 'Decorate Bromide' },
    { id: 'event', name: 'Event' },
    { id: 'festival', name: 'Festival' },
    { id: 'gacha', name: 'Gacha' },
    { id: 'girllist', name: 'Girl List' },
    { id: 'memories', name: 'Memories' },
    { id: 'ownerroom', name: 'Owner Room' },
    { id: 'shop', name: 'Shop' },
    { id: 'skill', name: 'Skill' }
  ];

  // Initialize site content
  useEffect(() => {
    const mockSiteContent: SiteContent[] = [
      {
        id: '1',
        page: 'HomePage',
        section: 'Welcome Message',
        content: 'Welcome to the ultimate DOAX Venus Vacation guide and reference tool.',
        lastModified: '2024-01-18',
        modifiedBy: 'Admin'
      },
      {
        id: '2',
        page: 'HomePage',
        section: 'Featured Content',
        content: 'Discover the latest swimsuits, characters, and gameplay tips.',
        lastModified: '2024-01-17',
        modifiedBy: 'Admin'
      },
      {
        id: '3',
        page: 'SettingsPage',
        section: 'Theme Options',
        content: 'Customize your experience with different themes and accessibility options.',
        lastModified: '2024-01-16',
        modifiedBy: 'Admin'
      }
    ];

    setSiteContent(mockSiteContent);
  }, []);

  const adminSections: AdminSection[] = [
    {
      id: 'documents',
      title: 'Documents',
      icon: BookOpen,
      description: 'Manage documentation and guides',
      lastUpdated: '2024-01-20',
      status: 'active'
    },
    {
      id: 'update-logs',
      title: 'Update Logs',
      icon: FileText,
      description: 'Manage version updates and changelogs',
      lastUpdated: '2024-01-20',
      status: 'active'
    },
    {
      id: 'csv-management',
      title: 'CSV Import/Export',
      icon: FileDown,
      description: 'Import and export data in CSV format',
      lastUpdated: '2024-01-20',
      status: 'active'
    }
  ];

  const handleSaveUpdateLog = async (log: UpdateLog) => {
    try {
      if (editingLog?.id) {
        // Update existing
        await updateUpdateLog(editingLog.id, log);
      } else {
        // Create new
        await addUpdateLog(log);
      }
      setEditingLog(null);
      setIsEditMode(false);
    } catch (error) {
      console.error('Error saving update log:', error);
      alert('Failed to save update log. Please try again.');
    }
  };


  const handleSaveDocument = (document: Document) => {
    if (editingDocument?.id) {
      updateDocument(editingDocument.id, document);
    } else {
      const newDocument = { ...document, id: Date.now().toString() };
      addDocument(newDocument);
    }
    setEditingDocument(null);
    setIsEditMode(false);
  };

  const handleDeleteDocument = (documentId: string) => {
    if (confirm('Are you sure you want to delete this document?')) {
      deleteDocument(documentId);
    }
  };

  // CSV Import/Export functions
  const exportToCSV = (data: any[], filename: string) => {
    if (data.length === 0) return;
    
    const headers = Object.keys(data[0]);
    const csvContent = [
      headers.join(','),
      ...data.map(row => 
        headers.map(header => {
          const value = row[header];
          // Handle arrays and objects
          if (Array.isArray(value)) {
            return `"${value.join('; ')}"`;
          }
          if (typeof value === 'object' && value !== null) {
            return `"${JSON.stringify(value)}"`;
          }
          // Escape quotes and wrap in quotes if contains comma or quote
          const stringValue = String(value || '');
          if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
            return `"${stringValue.replace(/"/g, '""')}"`;
          }
          return stringValue;
        }).join(',')
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleExportDocuments = () => {
    exportToCSV(documents, 'documents.csv');
  };

  const handleExportUpdateLogs = () => {
    exportToCSV(updateLogs, 'update-logs.csv');
  };

  // Enhanced file upload handler
  const processFile = async (file: File) => {
    if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
      alert('Please select a CSV file');
      return;
    }

    if (file.size > 10 * 1024 * 1024) { // 10MB limit
      alert('File size must be less than 10MB');
      return;
    }

    setUploadedFile(file);
    setIsProcessingFile(true);

    try {
      const text = await file.text();
      setCsvData(text);
      setIsProcessingFile(false);
    } catch (error) {
      alert('Error reading file');
      setIsProcessingFile(false);
      console.error('File reading error:', error);
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;
    await processFile(file);
  };

  // Drag and drop handlers
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      await processFile(files[0]);
    }
  };

  const parseCSVData = (csvText: string) => {
    const lines = csvText.trim().split('\n');
    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
    const data = lines.slice(1).map(line => {
      const values: string[] = [];
      let current = '';
      let inQuotes = false;
      
      for (let i = 0; i < line.length; i++) {
        const char = line[i];
        if (char === '"') {
          if (inQuotes && line[i + 1] === '"') {
            current += '"';
            i++; // Skip next quote
          } else {
            inQuotes = !inQuotes;
          }
        } else if (char === ',' && !inQuotes) {
          values.push(current.trim());
          current = '';
        } else {
          current += char;
        }
      }
      values.push(current.trim());

      const obj: any = {};
      headers.forEach((header, index) => {
        let value = values[index] || '';
        
        // Handle special fields
        if (header === 'tags' || header === 'changes') {
          obj[header] = value ? value.split('; ').map((v: string) => v.trim()) : [];
        } else if (header === 'isPublished') {
          obj[header] = value.toLowerCase() === 'true';
        } else {
          obj[header] = value;
        }
      });
      
      return obj;
    });

    return data;
  };

  const handleImportCSV = async () => {
    if (!csvData.trim()) {
      alert('Please paste CSV data or upload a file first');
      return;
    }

    try {
      const data = parseCSVData(csvData);

      if (csvImportType === 'documents') {
        // Validate and import documents
        let validDocuments = data.filter(doc => doc.title && doc.content);
        
        // Filter by page if specified
        if (importPage !== 'all') {
          validDocuments = validDocuments.filter(doc => 
            doc.category === importPage || doc.page === importPage
          );
        }

        validDocuments.forEach(doc => {
          if (!doc.id) doc.id = Date.now().toString() + Math.random().toString(36).substr(2, 9);
          if (!doc.createdAt) doc.createdAt = new Date().toISOString().split('T')[0];
          if (!doc.updatedAt) doc.updatedAt = new Date().toISOString().split('T')[0];
          if (!doc.author) doc.author = 'Admin';
          if (doc.isPublished === undefined) doc.isPublished = false;
          
          // Set category based on import page if specified
          if (importPage !== 'all' && !doc.category) {
            doc.category = importPage;
          }
          
          addDocument(doc as Document);
        });
        
        const pageText = importPage !== 'all' ? ` to ${availablePages.find(page => page.id === importPage)?.name || importPage} page` : '';
        alert(`Imported ${validDocuments.length} documents successfully${pageText}`);
      } else {
        // Validate and import update logs
        let validLogs = data.filter(log => log.version && log.title);
        
        // Process Update Logs
        validLogs.forEach(log => {
          if (!log.id) log.id = Date.now().toString() + Math.random().toString(36).substr(2, 9);
          if (!log.version) log.version = 'v1.0.0';
          if (!log.title) log.title = 'Untitled Update';
          if (!log.content) log.content = '';
          if (!log.date) log.date = new Date().toISOString().split('T')[0];
          if (log.isPublished === undefined) log.isPublished = false;
          if (!log.tags) log.tags = [];
        });
        
        // Add logs using context
        for (const log of validLogs) {
          try {
            await addUpdateLog(log);
          } catch (error) {
            console.error('Error adding update log:', log, error);
          }
        }
        
        alert(`Imported ${validLogs.length} update logs successfully`);
      }
      
      // Clear data after successful import
      setCsvData('');
      setUploadedFile(null);
      
      // Reset file input
      const fileInput = document.getElementById('csvFileInput') as HTMLInputElement;
      if (fileInput) fileInput.value = '';
      
    } catch (error) {
      alert('Error parsing CSV data. Please check the format.');
      console.error('CSV Import Error:', error);
    }
  };

  const renderCsvManagement = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Export Section */}
        <div className="doax-card p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <Download className="w-5 h-5 text-accent-pink" />
            Export Data
          </h3>
          <div className="space-y-4">
            <p className="text-muted-foreground text-sm">
              Export your data to CSV format for backup or external processing.
            </p>
            
            <div className="space-y-3">
              <Button 
                onClick={handleExportDocuments}
                className="w-full justify-start"
                variant="outline"
              >
                <FileDown className="w-4 h-4 mr-2" />
                Export Documents ({documents.length} items)
              </Button>
              
              <Button 
                onClick={handleExportUpdateLogs}
                className="w-full justify-start"
                variant="outline"
              >
                <FileDown className="w-4 h-4 mr-2" />
                Export Update Logs ({updateLogs.length} items)
              </Button>
            </div>
            
            <div className="mt-4 p-3 bg-muted/50 rounded-lg">
              <p className="text-xs text-muted-foreground">
                <strong>Note:</strong> Exported CSV files can be opened in Excel, Google Sheets, or any spreadsheet application.
              </p>
            </div>
          </div>
        </div>

        {/* Import Section */}
        <div className="doax-card p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <Upload className="w-5 h-5 text-accent-cyan" />
            Import Data
          </h3>
          <div className="space-y-4">
            <p className="text-muted-foreground text-sm">
              Import data from CSV format. Upload a file or paste CSV data below.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Import Type</label>
                <select
                  value={csvImportType}
                  onChange={(e) => setCsvImportType(e.target.value as 'documents' | 'update-logs')}
                  className="w-full px-3 py-2 border border-border rounded-lg bg-background"
                >
                  <option value="documents">Documents</option>
                  <option value="update-logs">Update Logs</option>
                </select>
              </div>
              
              {csvImportType === 'documents' && (
                <div>
                  <label className="block text-sm font-medium mb-2">Target Page</label>
                  <select
                    value={importPage}
                    onChange={(e) => setImportPage(e.target.value)}
                    className="w-full px-3 py-2 border border-border rounded-lg bg-background"
                  >
                    <option value="all">All Pages (Keep Original Categories)</option>
                    {availablePages.map(page => (
                      <option key={page.id} value={page.id}>
                        {page.name}
                      </option>
                    ))}
                  </select>
                </div>
              )}
            </div>
            
            {/* File Upload Section */}
            <div 
              className={`border-2 border-dashed rounded-lg p-6 text-center transition-all ${
                isDragging 
                  ? 'border-accent-cyan bg-accent-cyan/5 scale-105' 
                  : 'border-border hover:border-accent-cyan/50'
              }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
            >
              <div className="space-y-3">
                <div className="flex items-center justify-center">
                  <Upload className={`w-8 h-8 transition-colors ${
                    isDragging ? 'text-accent-cyan' : 'text-muted-foreground'
                  }`} />
                </div>
                <div>
                  <label htmlFor="csvFileInput" className="cursor-pointer">
                    <span className="text-accent-cyan hover:text-accent-cyan/80 font-medium">
                      Choose CSV file
                    </span>
                    <span className="text-muted-foreground"> or drag and drop here</span>
                  </label>
                  <input
                    id="csvFileInput"
                    type="file"
                    accept=".csv,text/csv"
                    onChange={handleFileUpload}
                    className="hidden"
                  />
                </div>
                {uploadedFile && (
                  <div className="flex items-center justify-center gap-2 text-sm text-accent-cyan bg-accent-cyan/10 rounded-lg p-2">
                    <FileUp className="w-4 h-4" />
                    <span className="font-medium">{uploadedFile.name}</span>
                    <span className="text-xs text-muted-foreground">
                      ({(uploadedFile.size / 1024).toFixed(1)} KB)
                    </span>
                    <button
                      onClick={() => {
                        setUploadedFile(null);
                        setCsvData('');
                        const fileInput = document.getElementById('csvFileInput') as HTMLInputElement;
                        if (fileInput) fileInput.value = '';
                      }}
                      className="text-red-500 hover:text-red-600 ml-2 p-1 rounded hover:bg-red-50"
                      title="Remove file"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </div>
                )}
                {isProcessingFile && (
                  <div className="flex items-center justify-center gap-2 text-sm text-accent-cyan">
                    <div className="animate-spin w-4 h-4 border-2 border-accent-cyan border-t-transparent rounded-full"></div>
                    <span>Processing file...</span>
                  </div>
                )}
                {isDragging && (
                  <div className="text-accent-cyan font-medium">
                    Drop CSV file here
                  </div>
                )}
              </div>
            </div>
            
            {/* Manual CSV Input */}
            <div>
              <label className="block text-sm font-medium mb-2">
                Or Paste CSV Data Manually
              </label>
              <textarea
                value={csvData}
                onChange={(e) => setCsvData(e.target.value)}
                rows={6}
                className="w-full px-3 py-2 border border-border rounded-lg bg-background resize-none text-sm font-mono"
                placeholder="Paste your CSV data here..."
              />
            </div>
            
            <Button 
              onClick={handleImportCSV}
              disabled={!csvData.trim() || isProcessingFile}
              className="w-full bg-gradient-to-r from-accent-cyan to-accent-purple"
            >
              <FileUp className="w-4 h-4 mr-2" />
              {isProcessingFile ? 'Processing...' : 'Import Data'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderUpdateLogs = () => (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold">Update Logs Management</h2>
        <Button 
          onClick={() => {
            setEditingLog({
              id: '',
              version: '',
              title: '',
              description: '',
              content: '',
              date: new Date().toISOString().split('T')[0],
              isPublished: false,
              tags: [],
              technicalDetails: [],
              bugFixes: [],
              screenshots: [],
              metrics: {
                performanceImprovement: '0%',
                userSatisfaction: '0%',
                bugReports: 0
              }
            });
            setIsEditMode(true);
          }}
          className="bg-gradient-to-r from-accent-pink to-accent-purple"
        >
          <Plus className="w-4 h-4 mr-2" />
          New Update Log
        </Button>
      </div>

      {isEditMode && editingLog ? (
        <div className="doax-card p-6">
          <h3 className="text-lg font-semibold mb-4">
            {editingLog.id ? 'Edit Update Log' : 'Create New Update Log'}
          </h3>
          
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Version</label>
                <Input
                  value={editingLog.version}
                  onChange={(e) => setEditingLog({ ...editingLog, version: e.target.value })}
                  placeholder="v2.1.0"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Date</label>
                <Input
                  type="date"
                  value={editingLog.date}
                  onChange={(e) => setEditingLog({ ...editingLog, date: e.target.value })}
                />
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2">Title</label>
              <Input
                value={editingLog.title}
                onChange={(e) => setEditingLog({ ...editingLog, title: e.target.value })}
                placeholder="Update title..."
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2">Description</label>
              <Input
                value={editingLog.description}
                onChange={(e) => setEditingLog({ ...editingLog, description: e.target.value })}
                placeholder="Brief description..."
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2">Content</label>
              <div className="border border-border rounded-lg overflow-hidden">
                <TiptapEditor
                  content={editingLog.content}
                  onChange={(content) => setEditingLog({ ...editingLog, content })}
                  editable={true}
                  placeholder="Describe the update in detail..."
                  showToolbar={true}
                  showCharacterCount={true}
                  mode="minimal"
                  className="min-h-[200px] border-0"
                />
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2">Tags (comma separated)</label>
              <Input
                value={editingLog.tags.join(', ')}
                onChange={(e) => setEditingLog({ 
                  ...editingLog, 
                  tags: e.target.value.split(',').map(tag => tag.trim()).filter(Boolean)
                })}
                placeholder="ui, ux, bugfix, performance..."
              />
            </div>
            
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="publish-log"
                checked={editingLog.isPublished}
                onChange={(e) => setEditingLog({ ...editingLog, isPublished: e.target.checked })}
                className="rounded"
              />
              <label htmlFor="publish-log" className="text-sm">
                Publish this update log
              </label>
            </div>
            
            <div className="flex gap-2">
              <Button onClick={() => handleSaveUpdateLog(editingLog)}>
                <Save className="w-4 h-4 mr-2" />
                Save
              </Button>
              <Button 
                variant="outline" 
                onClick={() => {
                  setEditingLog(null);
                  setIsEditMode(false);
                }}
              >
                Cancel
              </Button>
            </div>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          {updateLogs.map(log => (
            <div key={log.id} className="doax-card p-6">
              <div className="flex justify-between items-start mb-4">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="text-xl font-semibold">{log.version}</h3>
                    <Badge variant={log.isPublished ? 'default' : 'secondary'}>
                      {log.isPublished ? 'Published' : 'Draft'}
                    </Badge>
                  </div>
                  <h4 className="text-lg mb-2">{log.title}</h4>
                  <p className="text-muted-foreground mb-3">{log.content}</p>
                  <div className="space-y-1">
                    {log.tags.map((tag, index) => (
                      <div key={index} className="flex items-center gap-2 text-sm">
                        <div className="w-1 h-1 bg-accent-pink rounded-full"></div>
                        <span>#{tag}</span>
                      </div>
                    ))}
                  </div>
                  <p className="text-sm text-muted-foreground mt-3">
                    <Calendar className="w-4 h-4 inline mr-1" />
                    {log.date}
                  </p>
                </div>
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      setEditingLog(log);
                      setIsEditMode(true);
                    }}
                  >
                    <Edit3 className="w-4 h-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={async () => {
                      if (confirm('Are you sure you want to delete this update log?')) {
                        try {
                          await deleteUpdateLog(log.id);
                        } catch (error) {
                          console.error('Error deleting update log:', error);
                          alert('Failed to delete update log. Please try again.');
                        }
                      }
                    }}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );

  const renderDocuments = () => (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold">Document Management</h2>
        <div className="flex gap-2">
          <Button onClick={() => {
            setEditingDocument({
              id: '',
              title: '',
              content: '',
              category: 'tutorial',
              tags: [],
              author: 'Admin',
              createdAt: new Date().toISOString().split('T')[0],
              updatedAt: new Date().toISOString().split('T')[0],
              isPublished: false
            });
            setIsEditMode(true);
          }}>
            <Plus className="w-4 h-4 mr-2" />
            New Document
          </Button>
        </div>
      </div>

      {isEditMode && editingDocument ? (
        <div className={cn(
          "doax-card transition-all duration-300",
          isFullscreen ? "fixed inset-4 z-50 overflow-auto" : "relative"
        )}>
          <div className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold">
                {editingDocument.id ? 'Edit Document' : 'Create New Document'}
              </h3>

              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsPreviewMode(!isPreviewMode)}
                  className={cn(
                    "transition-colors",
                    isPreviewMode && "bg-accent-cyan/20 text-accent-cyan"
                  )}
                >
                  <Eye className="w-4 h-4 mr-2" />
                  {isPreviewMode ? 'Edit' : 'Preview'}
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsFullscreen(!isFullscreen)}
                >
                  <Maximize2 className="w-4 h-4" />
                </Button>

                {isFullscreen && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsFullscreen(false)}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                )}
              </div>
            </div>

            <div className={cn(
              "grid gap-6",
              isFullscreen ? "grid-cols-1" : "grid-cols-1 lg:grid-cols-3"
            )}>
              {/* Document Metadata */}
              <div className={cn(
                "space-y-4",
                isFullscreen ? "mb-6" : "lg:col-span-1"
              )}>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-1 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Title</label>
                    <Input
                      value={editingDocument.title}
                      onChange={(e) => setEditingDocument({ ...editingDocument, title: e.target.value })}
                      placeholder="Document title..."
                      className="transition-all focus:ring-2 focus:ring-accent-cyan/20"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">Category</label>
                    <select
                      value={editingDocument.category}
                      onChange={(e) => setEditingDocument({ ...editingDocument, category: e.target.value })}
                      className="w-full px-3 py-2 border border-border rounded-lg bg-background transition-all focus:ring-2 focus:ring-accent-cyan/20 focus:border-accent-cyan"
                    >
                      {documentCategories.map(category => (
                        <option key={category.id} value={category.id}>
                          {category.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="md:col-span-2 lg:col-span-1">
                    <label className="block text-sm font-medium mb-2">Tags (comma separated)</label>
                    <Input
                      value={editingDocument.tags.join(', ')}
                      onChange={(e) => setEditingDocument({
                        ...editingDocument,
                        tags: e.target.value.split(',').map(tag => tag.trim()).filter(Boolean)
                      })}
                      placeholder="tutorial, guide, beginner..."
                      className="transition-all focus:ring-2 focus:ring-accent-cyan/20"
                    />
                  </div>

                  <div className="md:col-span-2 lg:col-span-1">
                    <div className="flex items-center gap-2 p-3 bg-muted/30 rounded-lg">
                      <input
                        type="checkbox"
                        id="admin-published"
                        checked={editingDocument.isPublished}
                        onChange={(e) => setEditingDocument({ ...editingDocument, isPublished: e.target.checked })}
                        className="rounded transition-colors"
                      />
                      <label htmlFor="admin-published" className="text-sm font-medium">
                        Publish this document
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              {/* Content Editor */}
              <div className={cn(
                "space-y-4",
                isFullscreen ? "" : "lg:col-span-2"
              )}>
                <div className="flex items-center justify-between">
                  <label className="block text-sm font-medium">
                    Content
                  </label>
                  <div className="text-xs text-muted-foreground">
                    Rich text editor with live preview
                  </div>
                </div>

                <div className={cn(
                  "border border-border rounded-xl overflow-hidden",
                  isFullscreen && "min-h-[calc(100vh-300px)]"
                )}>
                  <TiptapEditor
                    content={editingDocument.content}
                    onChange={(content) => setEditingDocument({ ...editingDocument, content })}
                    editable={!isPreviewMode}
                    placeholder="Start writing your document content..."
                    showToolbar={!isPreviewMode}
                    showCharacterCount={true}
                    showWordCount={true}
                    mode="full"
                    className={cn(
                      "border-0",
                      isFullscreen ? "min-h-[calc(100vh-350px)]" : "min-h-[400px]"
                    )}
                  />
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between mt-8 pt-6 border-t border-border">
              <div className="text-sm text-muted-foreground">
                {editingDocument.id ? 'Last saved: ' + editingDocument.updatedAt : 'New document'}
              </div>

              <div className="flex gap-3">
                <Button
                  variant="outline"
                  onClick={() => {
                    setEditingDocument(null);
                    setIsEditMode(false);
                    setIsPreviewMode(false);
                    setIsFullscreen(false);
                  }}
                >
                  <X className="w-4 h-4 mr-2" />
                  Cancel
                </Button>

                <Button
                  onClick={() => handleSaveDocument(editingDocument)}
                  className="bg-gradient-to-r from-accent-cyan to-accent-purple"
                >
                  <Save className="w-4 h-4 mr-2" />
                  {editingDocument.id ? 'Save Changes' : 'Create Document'}
                </Button>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="grid gap-4">
            {documents.map(document => (
              <div key={document.id} className="doax-card p-6">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-lg font-semibold">{document.title}</h3>
                      {!document.isPublished && (
                        <Badge variant="secondary" className="text-xs">Draft</Badge>
                      )}
                      <Badge variant="outline" className="text-xs">
                        {documentCategories.find(cat => cat.id === document.category)?.name || document.category}
                      </Badge>
                    </div>
                    <p className="text-muted-foreground mb-3 line-clamp-2">
                      {document.content.split('\n').find(line => line.trim() && !line.startsWith('#'))?.slice(0, 150)}
                      {document.content.length > 150 && '...'}
                    </p>
                    <div className="flex flex-wrap gap-1 mb-3">
                      {document.tags.slice(0, 3).map(tag => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          <Tags className="w-3 h-3 mr-1" />
                          {tag}
                        </Badge>
                      ))}
                      {document.tags.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{document.tags.length - 3} more
                        </Badge>
                      )}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Last updated: {document.updatedAt} by {document.author}
                    </div>
                  </div>
                  <div className="flex gap-2 ml-4">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => {
                        setEditingDocument(document);
                        setIsEditMode(true);
                      }}
                    >
                      <Edit3 className="w-4 h-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDeleteDocument(document.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          {documents.length === 0 && (
            <div className="doax-card p-8 text-center">
              <BookOpen className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No documents yet</h3>
              <p className="text-muted-foreground mb-4">Create your first document to get started</p>
              <Button onClick={() => {
                setEditingDocument({
                  id: '',
                  title: '',
                  content: '',
                  category: 'tutorial',
                  tags: [],
                  author: 'Admin',
                  createdAt: new Date().toISOString().split('T')[0],
                  updatedAt: new Date().toISOString().split('T')[0],
                  isPublished: false
                });
                setIsEditMode(true);
              }}>
                <Plus className="w-4 h-4 mr-2" />
                Create Document
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  );


  const renderTabContent = () => {
    switch (activeTab) {
      case 'documents':
        return renderDocuments();
      case 'update-logs':
        return renderUpdateLogs();
      case 'csv-management':
        return renderCsvManagement();
      default:
        return (
          <div className="doax-card p-8 text-center">
            <Settings className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">This section is under development</p>
          </div>
        );
    }
  };

  return (
    <Container>
      {/* Header */}
      <Section
        title="Admin Panel"
        description="Manage website content, updates, and system settings"
        action={
          <StatusBadge status="success">
            System Online
          </StatusBadge>
        }
      />

      {/* Tab Navigation */}
      <Card className="p-2">
        <Inline spacing="sm" wrap>
          {adminSections.map(section => {
            const IconComponent = section.icon;
            return (
              <button
                key={section.id}
                onClick={() => setActiveTab(section.id)}
                className={cn(
                  'flex-1 px-4 py-3 rounded-lg transition-all duration-200 flex items-center justify-center gap-2 font-medium min-w-0',
                  'hover:scale-105 hover:shadow-md',
                  activeTab === section.id
                    ? 'bg-gradient-to-r from-accent-pink to-accent-purple text-white shadow-lg'
                    : 'bg-muted/50 hover:bg-muted text-muted-foreground hover:text-foreground'
                )}
              >
                <IconComponent className="w-4 h-4" />
                <span>{section.title}</span>
              </button>
            );
          })}
        </Inline>
      </Card>

      {/* Main Content */}
      <div className="w-full">
        {renderTabContent()}
      </div>
    </Container>
  );
};

export default AdminPage; 